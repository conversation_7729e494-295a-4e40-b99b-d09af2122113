import { api } from './api';

// Helper function to extract data from standardized API response
const extractData = (response: any) => {
  if (response.success) {
    return response.data;
  } else {
    throw new Error(response.message || 'API request failed');
  }
};

// Types
export interface User {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'user' | 'admin';
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Product {
  id: string;
  _id: string;
  name: string;
  description: string;
  price: number;
  original_price: number;
  stock: number;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  images: string[];
  is_active: boolean;
  is_featured: boolean;
  rating: number;
  review_count: number;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  _id: string;
  name: string;
  description?: string;
  slug: string;
  image?: string;
  parent?: {
    _id: string;
    id?: string;
    name: string;
    slug?: string;
  } | null;
  product_count: number;
  subcategories: Array<{
    id: string;
    name: string;
    slug: string;
  }>;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  _id: string;
  orderNumber: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    product: {
      id: string;
      name: string;
    };
    quantity: number;
    price: number;
    subtotal: number;
  }>;
  total: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'completed' | 'failed';
  paymentMethod: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Coupon {
  id: string;
  _id: string;
  code: string;
  description: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  minimumOrderAmount: number;
  maximumDiscountAmount?: number;
  validFrom: string;
  validUntil: string;
  usageLimit: number;
  usedCount: number;
  userUsageLimit: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface DashboardStats {
  productCount: number;
  categoryCount: number;
  orderCount: number;
  userCount: number;
  totalSales: number;
  monthlyStats: {
    orders: number;
    sales: number;
    newUsers: number;
  };
}

// Auth Service
export const authService = {
  async login(email: string, password: string): Promise<{ user: User; token: string }> {
    const response = await api.post('/auth/login', { email, password });
    const data = extractData(response);

    // Store token after successful login
    if (data.token) {
      if (typeof window !== 'undefined') {
        localStorage.setItem('token', data.token);
        localStorage.setItem('authToken', data.token);
      }
    }

    return data;
  },

  async getProfile(): Promise<{ user: User }> {
    const response = await api.get('/auth/profile');
    return extractData(response);
  },

  async updateProfile(data: Partial<User>): Promise<{ user: User }> {
    const response = await api.put('/auth/profile', data);
    return extractData(response);
  },

  async logout(): Promise<void> {
    // Clear token from localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
      localStorage.removeItem('authToken');
    }
  },

  async checkAuth(): Promise<boolean> {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      if (!token) return false;

      await this.getProfile();
      return true;
    } catch (error) {
      // Clear invalid token
      this.logout();
      return false;
    }
  }
};



// Product Service
export const productService = {
  async getProducts(params?: { page?: number; limit?: number; search?: string }): Promise<Product[]> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);

    const query = queryParams.toString();
    const response = await api.get(`/products${query ? `?${query}` : ''}`);
    return extractData(response);
  },

  async getProduct(id: string): Promise<Product> {
    const response = await api.get(`/products/${id}`);
    return extractData(response);
  },

  async createProduct(data: FormData): Promise<Product> {
    const response = await api.post('/products', data);
    return extractData(response);
  },

  async updateProduct(id: string, data: FormData): Promise<Product> {
    const response = await api.put(`/products/${id}`, data);
    return extractData(response);
  },

  async deleteProduct(id: string): Promise<void> {
    const response = await api.delete(`/products/${id}`);
    return extractData(response);
  },

  async updateInventory(id: string, stock: number): Promise<Product> {
    const response = await api.patch(`/products/${id}/inventory`, { stock, operation: 'set' });
    return extractData(response);
  }
};

// Category Service
export const categoryService = {
  async getCategories(): Promise<Category[]> {
    const response = await api.get('/categories');
    return extractData(response);
  },

  async getCategory(id: string): Promise<Category> {
    const response = await api.get(`/categories/${id}`);
    return extractData(response);
  },

  async createCategory(data: FormData): Promise<Category> {
    const response = await api.post('/categories', data);
    return extractData(response);
  },

  async updateCategory(id: string, data: FormData): Promise<Category> {
    const response = await api.put(`/categories/${id}`, data);
    return extractData(response);
  },

  async deleteCategory(id: string): Promise<void> {
    const response = await api.delete(`/categories/${id}`);
    return extractData(response);
  }
};

// Order Service
export const orderService = {
  async getOrders(params?: { page?: number; limit?: number; status?: string }): Promise<Order[]> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.status) queryParams.append('status', params.status);

    const query = queryParams.toString();
    const response = await api.get(`/orders${query ? `?${query}` : ''}`);
    return extractData(response);
  },

  async getOrder(id: string): Promise<Order> {
    const response = await api.get(`/orders/${id}`);
    return extractData(response);
  },

  async updateOrderStatus(id: string, status: Order['status']): Promise<Order> {
    const response = await api.patch(`/orders/${id}/status`, { status });
    return extractData(response);
  }
};

// Dashboard Service
export const dashboardService = {
  async getStats(): Promise<DashboardStats> {
    const response = await api.get('/admin/stats');
    return extractData(response);
  },

  async getRecentOrders(): Promise<Order[]> {
    const response = await api.get('/orders?limit=5');
    return extractData(response);
  }
};

// User Service
export const userService = {
  async getUsers(params?: { page?: number; limit?: number; search?: string }): Promise<User[]> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.search) queryParams.append('search', params.search);

    const query = queryParams.toString();
    const response = await api.get(`/users${query ? `?${query}` : ''}`);
    // Backend userController doesn't use standardized response format
    // It returns {users, totalUsers, totalPages, currentPage} directly
    return (response as any).users || response;
  },

  async getUser(id: string): Promise<User> {
    const response = await api.get(`/users/${id}`);
    return response as User; // Backend returns user directly
  },

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    const response = await api.put(`/users/${id}`, data);
    return (response as any).user || response; // Backend returns {message, user}
  },

  async deleteUser(id: string): Promise<void> {
    await api.delete(`/users/${id}`);
    // Backend returns {message} for delete
  }
};

// Coupon Service
export const couponService = {
  async getCoupons(params?: { page?: number; limit?: number }): Promise<Coupon[]> {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());

    const query = queryParams.toString();
    const response = await api.get(`/coupons${query ? `?${query}` : ''}`);
    return extractData(response);
  },

  async createCoupon(data: Partial<Coupon>): Promise<Coupon> {
    const response = await api.post('/coupons', data);
    return extractData(response);
  },

  async updateCoupon(id: string, data: Partial<Coupon>): Promise<Coupon> {
    const response = await api.put(`/coupons/${id}`, data);
    return extractData(response);
  },

  async deleteCoupon(id: string): Promise<void> {
    const response = await api.delete(`/coupons/${id}`);
    return extractData(response);
  },

  async validateCoupon(code: string, orderAmount: number, cartItems: any[]): Promise<any> {
    const response = await api.post('/coupons/validate', { code, orderAmount, cartItems });
    return extractData(response);
  }
};